:root {
            --primary: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #dbeafe;
            --secondary: #64748b;
            --success: #10b981;
            --success-light: #d1fae5;
            --warning: #f59e0b;
            --warning-light: #fef3c7;
            --danger: #ef4444;
            --danger-light: #fee2e2;
            --light: #f8fafc;
            --dark: #1e293b;
            --sidebar-width: 240px;
            --header-height: 60px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', 'Microsoft YaHei', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f1f5f9;
    color: var(--dark);
    overflow-x: hidden;
}

/* 登录页面样式 */
.login-container {
    display: flex;
    min-height: 100vh;
    background: #f8fafc;
    justify-content: center;
    align-items: center;
}

.login-right {
    width: 500px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 3rem;
    background: white;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border-radius: 12px;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header h2 {
    font-size: 2.2rem;
    color: var(--primary);
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.login-header p {
    color: var(--secondary);
    font-size: 1.1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark);
    font-size: 1rem;
}

.form-control {
    width: 100%;
    padding: 0.9rem 1.2rem;
    border: 1px solid #cbd5e1;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
}

.remember-forgot {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.remember {
    display: flex;
    align-items: center;
}

.remember input {
    margin-right: 0.5rem;
    width: 18px;
    height: 18px;
}

.remember label {
    color: var(--dark);
    font-size: 0.95rem;
}

.forgot-password {
    color: var(--primary);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
}

.forgot-password:hover {
    text-decoration: underline;
}

.btn {
    display: block;
    width: 100%;
    padding: 1rem;
    background-color: var(--primary);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 4px 6px rgba(37, 99, 235, 0.2);
}

.btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(37, 99, 235, 0.3);
}

.btn:active {
    transform: translateY(0);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--primary);
    color: var(--primary);
}

.btn-outline:hover {
    background: var(--primary-light);
}

.btn-success {
    background: var(--success);
}

.btn-success:hover {
    background: #0da271;
}

.btn-danger {
    background: var(--danger);
}

.btn-danger:hover {
    background: #dc2626;
}

.language-selector {
    display: flex;
    justify-content: center;
    margin-top: 1.5rem;
    color: var(--secondary);
    font-size: 0.95rem;
}

.language-selector a {
    color: var(--primary);
    text-decoration: none;
    margin: 0 0.5rem;
    font-weight: 500;
}

.login-footer {
    text-align: center;
    margin-top: 2rem;
    color: var(--secondary);
    font-size: 0.9rem;
}

/* 主应用样式 */
.app-container {
    display: none;
    min-height: 100vh;
    background: #f8fafc;
}

.app-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: var(--header-height);
    background: white;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    padding: 0 2rem;
    z-index: 1000;
}

.logo {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary);
}

.logo i {
    margin-right: 0.5rem;
    font-size: 1.8rem;
}

.app-title {
    margin-left: 1.5rem;
    font-size: 1.2rem;
    color: var(--dark);
    flex: 1;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: var(--primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.user-name {
    font-weight: 500;
}

.app-main {
    display: flex;
    margin-top: var(--header-height);
    min-height: calc(100vh - var(--header-height));
}

.sidebar {
    width: var(--sidebar-width);
    background: var(--dark);
    color: white;
    height: calc(100vh - var(--header-height));
    position: fixed;
    overflow-y: auto;
    padding: 1.5rem 0;
    transition: all 0.3s;
    z-index: 900;
}

.sidebar-menu {
    list-style: none;
}

.menu-title {
    padding: 0.8rem 1.5rem;
    color: #94a3b8;
    font-size: 0.85rem;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 1px;
    margin-top: 1rem;
}

.menu-title:first-child {
    margin-top: 0;
}

.menu-item {
    padding: 0.8rem 1.5rem;
    display: flex;
    align-items: center;
    color: #cbd5e1;
    text-decoration: none;
    transition: all 0.2s;
    font-weight: 500;
    cursor: pointer;
    border-left: 3px solid transparent;
}

.menu-item i {
    margin-right: 0.8rem;
    font-size: 1.1rem;
    width: 24px;
    text-align: center;
}

.menu-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.menu-item.active {
    background: rgba(37, 99, 235, 0.2);
    color: white;
    border-left: 3px solid var(--primary);
}

.submenu {
    list-style: none;
    padding-left: 3rem;
    margin-top: 0.3rem;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.menu-item.expanded + .submenu {
    max-height: 500px;
}

.submenu-item {
    padding: 0.6rem 0;
    color: #94a3b8;
    font-size: 0.95rem;
    cursor: pointer;
    transition: color 0.2s;
    position: relative;
}

.submenu-item:before {
    content: "";
    position: absolute;
    left: -15px;
    top: 50%;
    width: 6px;
    height: 6px;
    background: #94a3b8;
    border-radius: 50%;
    transform: translateY(-50%);
}

.submenu-item:hover {
    color: white;
}

.submenu-item.active {
    color: white;
    font-weight: 500;
}

.submenu-item.active:before {
    background: var(--primary);
}

.content {
    flex: 1;
    margin-left: var(--sidebar-width);
    padding: 2rem;
    transition: all 0.3s;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.page-title {
    font-size: 1.8rem;
    color: var(--dark);
    font-weight: 700;
}

.page-actions {
    display: flex;
    gap: 0.8rem;
}

.card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
    overflow: hidden;
    transition: all 0.3s;
}

.card:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.2rem 1.5rem;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.card-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--dark);
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.card-body {
    padding: 1.5rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-label {
    font-size: 0.95rem;
    color: var(--secondary);
    margin-bottom: 0.3rem;
}

.info-value {
    font-size: 1.15rem;
    font-weight: 500;
    color: var(--dark);
}

table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
    font-size: 0.95rem;
}

th {
    background: #f1f5f9;
    text-align: left;
    padding: 1rem;
    font-weight: 600;
    color: var(--dark);
    border-bottom: 2px solid #e2e8f0;
}

td {
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
    color: var(--dark);
}

tr:hover td {
    background: #f8fafc;
}

.action-cell {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    background: transparent;
    font-size: 0.9rem;
}

.edit-btn {
    color: var(--primary);
}

.edit-btn:hover {
    background: var(--primary-light);
}

.delete-btn {
    color: var(--danger);
}

.delete-btn:hover {
    background: var(--danger-light);
}

.save-btn {
    color: var(--success);
}

.save-btn:hover {
    background: var(--success-light);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 1.8rem;
    transition: all 0.3s;
    border-top: 4px solid var(--primary);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.stat-card.warning {
    border-top-color: var(--warning);
}

.stat-card.success {
    border-top-color: var(--success);
}

.stat-title {
    font-size: 1.05rem;
    color: var(--secondary);
    margin-bottom: 0.8rem;
    display: flex;
    align-items: center;
}

.stat-title i {
    margin-right: 0.5rem;
}

.stat-value {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--dark);
}

.stat-trend {
    display: flex;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
}

.trend-up {
    color: var(--success);
}

.trend-down {
    color: var(--danger);
}

.chart-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 1.8rem;
    margin-bottom: 2rem;
}

.chart-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--dark);
    display: flex;
    align-items: center;
}

.chart-title i {
    margin-right: 0.8rem;
    color: var(--primary);
}

.chart-placeholder {
    height: 320px;
    background: #f8fafc;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--secondary);
    font-weight: 500;
    flex-direction: column;
}

.chart-placeholder i {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    color: #cbd5e1;
}

.order-notes {
    background: #f0fdfa;
    border-left: 4px solid var(--success);
    padding: 1.2rem;
    border-radius: 6px;
    margin-top: 1.5rem;
}

.order-notes p {
    color: var(--dark);
    font-size: 1rem;
    margin: 0;
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1100;
    justify-content: center;
    align-items: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 500px;
    max-width: 90%;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    max-height: 80vh; /* 限制最大高度为视口的80% */
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 1.2rem 1.5rem;
    background: var(--primary);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0; /* 防止头部被压缩 */
}

.modal-title {
    font-size: 1.3rem;
    font-weight: 600;
}

.modal-body {
    padding: 1.5rem;
    overflow-y: auto; /* 内容超出时显示垂直滚动条 */
    flex-grow: 1; /* 占据剩余空间 */
    max-height: calc(80vh - 5rem); /* 计算最大高度，减去头部和底部可能的高度 */
}

.modal-footer {
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    border-top: 1px solid #e0e0e0;
    flex-shrink: 0; /* 防止底部被压缩 */
}
.close-modal {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.2s;
}

.close-modal:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 1.5rem;
}

.form-row {
    margin-bottom: 1.2rem;
}

.form-row label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark);
}

.form-row input, .form-row select {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid #cbd5e1;
    border-radius: 8px;
    font-size: 1rem;
}

.modal-footer {
    padding: 1.2rem 1.5rem;
    background: #f8fafc;
    display: flex;
    justify-content: flex-end;
    gap: 0.8rem;
}

.highlight-row {
    animation: highlight 2s ease;
}

@keyframes highlight {
    0% { background-color: rgba(16, 185, 129, 0.3); }
    100% { background-color: transparent; }
}

/* 响应式调整 */
@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
    
    .content {
        margin-left: 0;
    }
    
    .menu-toggle {
        display: block;
        margin-right: 1rem;
        background: none;
        border: none;
        font-size: 1.5rem;
        color: var(--dark);
        cursor: pointer;
    }
}

@media (max-width: 768px) {
    .login-container {
        flex-direction: column;
    }
    
    .login-left {
        min-height: 300px;
    }
    
    .login-right {
        width: 100%;
    }
    
    .features {
        flex-wrap: wrap;
    }
}

.card-header .btn.btn-outline.small-btn {
    margin-left: auto;
}
#rewardConfigModal .btn-mini {
    height: 22px;
    line-height: 18px;
    padding: 0 8px;
    font-size: 12px;
    min-width: 0;
}

.header-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 16px; /* 控制三个按钮之间的间距，按需调整 */
}

.btn.btn-outline {
    background: #e0edff;           /* 浅蓝色背景 */
    border: 2px solid #2563eb;     /* 蓝色边框 */
    color: #2563eb;                /* 蓝色文字 */
    transition: background 0.2s, color 0.2s, border-color 0.2s;
    box-shadow: none;
}

.btn.btn-outline:hover {
    background: #2563eb;           /* 悬停时深蓝 */
    color: #fff;                   /* 悬停时白字 */
    border-color: #2563eb;
}

/* 步骤容器 - 使用网格布局确保对齐 */
.process-steps {
  display: grid;
  grid-template-columns: 1fr 3fr auto; /* 左中右三列比例 */
  align-items: center;
  gap: 12px;
  width: 100%;

  border: 1px solid #e2e8f0;  /* 浅灰色边框 */
  border-radius: 8px;         /* 圆角可选 */
  padding: 12px 16px;         /* 内边距让内容更清晰 */
  background-color: #fff;     /* 可选背景色，提升可视性 */
}


/* 步骤名称 */
.step-name {
  padding: 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 进度条容器 */

.progress-container {
  display: flex;
  align-items: center;
  justify-content: center; /* 水平居中 */
  gap: 8px;
  /* margin: 0 auto; 让块元素水平居中 */
}

.progress-bar {
  width: 600px;
  height: 16px;
  background: #edf2f7;
  border-radius: 6px;
  overflow: hidden;
}

.progress {
  height: 100%;
  border-radius: 6px;
  transition: width 0.5s ease;
}

/* 进度百分比标签 */
.progress-percent {
  min-width: 40px;
  text-align: right;
  font-size: 12px;
  color: #718096;
}

/* 操作按钮容器 - 右对齐 */
.step-action {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

/* 按钮样式优化 */
.btn-mini {
  font-size: 12px;
  padding: 5px 12px;
  border-radius: 4px;
  background: #3182ce;
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-mini:hover {
  background: #2b6cb0;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}
/* 进度条颜色动态变化 */
.progress[data-progress="0"] { background: #e0e0e0; }
.progress[data-progress^="1"] { background: #ff9800; }
.progress[data-progress^="2"] { background: #ff9800; }
.progress[data-progress^="3"] { background: #ffc107; }
.progress[data-progress^="4"] { background: #ffc107; }
.progress[data-progress^="5"] { background: #8bc34a; }
.progress[data-progress^="6"] { background: #8bc34a; }
.progress[data-progress^="7"] { background: #4caf50; }
.progress[data-progress^="8"] { background: #4caf50; }
.progress[data-progress^="9"] { background: #2e7d32; }
.progress[data-progress="100"] { background: #1b5e20; }
/* 任务卡片悬停效果 */
.process-task {
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin: 12px 0;
  background: white;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.process-task:hover {
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  transform: translateY(-2px);
}

/* 层级指示器 */
.process-task-level2 {
  margin-left: 32px;
  background: #f9fbfd;
  border-left: 3px solid #63b3ed;
}

.process-task-level3 {
  margin-left: 64px;
  background: #f5f9ff;
  border-left: 3px solid #4299e1;
}

.process-task-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 18px;
  flex-wrap: wrap;
}
.process-task-info {
  display: flex;
  gap: 18px;
  flex-wrap: wrap;
  align-items: center;
}
.process-task-expand {
  margin-left: auto;
  display: flex;
  align-items: center;
}
.process-task-expand .btn-mini {
    min-width: 100px;
    padding-left: 18px;
    padding-right: 18px;
    font-size: 14px;
}

.process-add-task-btn {
    min-width: 80px;
    padding: 6px 18px;
    font-size: 14px;
    height: 32px;
    border-radius: 4px;
    background: #e0edff;
    color: #2563eb;
    border: 2px solid #2563eb;
    margin-left: 14px;
    transition: background 0.2s, color 0.2s, border-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}
.process-add-task-btn:hover {
    background: #2563eb;
    color: #fff;
    border-color: #2563eb;
}
.process-delete-task-btn {
    min-width: 80px;
    padding: 6px 18px;
    font-size: 14px;
    height: 32px;
    border-radius: 4px;
    background: #ffeaea;
    color: #e53e3e;
    border: 2px solid #e53e3e;
    margin-left: 14px;
    transition: background 0.2s, color 0.2s, border-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}
.process-delete-task-btn:hover {
    background: #e53e3e;
    color: #fff;
    border-color: #e53e3e;
}

.time-selector .time-btn {
    padding: 6px 12px;
    border: 1px solid #ccc;
    background-color: #fff;
    cursor: pointer;
    border-radius: 4px;
    margin-right: 5px;
    transition: background-color 0.3s, color 0.3s;
}

/* 选中状态的样式（变蓝） */
.time-selector .time-btn.active {
    background-color: #007bff; /* 蓝色背景 */
    color: #fff; /* 白色文字 */
    border-color: #007bff;
}

/* 鼠标悬停效果 */
.time-selector .time-btn:hover {
    background-color: #e9ecef;
}
/* 日历筛选器样式 */
.calendar-controls {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.calendar-filters {
    display: flex;
    gap: 15px;
    align-items: center;
}

.calendar-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
}

.calendar-select:hover {
    border-color: #4361ee;
}

.calendar-select:focus {
    outline: none;
    border-color: #4361ee;
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.1);
}

/* 日历图表完整样式 */
.calendar-chart {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 10px;
}

.calendar-nav {
    background: none;
    border: none;
    font-size: 18px;
    color: #666;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.calendar-nav:hover {
    background: #f0f0f0;
    color: #333;
}

.calendar-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    margin-bottom: 5px;
}

.calendar-weekday {
    text-align: center;
    padding: 10px 5px;
    font-size: 12px;
    font-weight: 600;
    color: #666;
    background: #f8f9fa;
    border-radius: 4px;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
}

.calendar-day {
    min-height: 60px;
    padding: 8px;
    background: white;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    border-radius: 6px;
    border: 1px solid #eee;
}

.calendar-day:hover {
    background: #f5f5f5;
    transform: scale(1.02);
}

.calendar-day.today {
    border: 2px solid #4361ee;
    background: #e3f2fd;
}

.calendar-day.other-month {
    background: #f9f9f9;
    color: #ccc;
}

.calendar-day.no-work {
    background: #f5f5f5;
    color: #999;
}

.calendar-day.light-work {
    background: #e3f2fd;
    color: #1976d2;
}

.calendar-day.has-work {
    background: #bbdefb;
    color: #0d47a1;
}

.calendar-day.heavy-work {
    background: #ffcdd2;
    color: #c62828;
}

.calendar-day-number {
    font-weight: bold;
    font-size: 14px;
}

.calendar-day-hours {
    font-size: 11px;
    color: #666;
    margin-top: 4px;
}

.calendar-day-note {
    font-size: 10px;
    color: #007bff;
    margin-top: 2px;
    background: rgba(0, 123, 255, 0.1);
    padding: 1px 3px;
    border-radius: 2px;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.calendar-legend {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
}

.legend-none {
    background: #f5f5f5;
}

.legend-light {
    background: #e3f2fd;
}

.legend-normal {
    background: #bbdefb;
}

.legend-heavy {
    background: #ffcdd2;
}
/* 调整操作按钮的宽度 */
.action-btn {
    padding: 6px 12px;
    margin: 0 3px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    min-width: 70px; /* 设置最小宽度 */
    white-space: nowrap; /* 防止文字换行 */
    justify-content: center;
}

.edit-btn {
    background: #3b82f6;
    color: white;
    min-width: 75px; /* 编辑按钮稍微宽一点 */
}

.delete-btn {
    background: #ef4444;
    color: white;
    min-width: 75px; /* 删除按钮稍微宽一点 */
}

/* 操作单元格也需要调整 */
.action-cell {
    min-width: 180px; /* 增加操作列的最小宽度 */
    white-space: nowrap;
}

/* 如果按钮在表格中，确保表格单元格有足够空间 */
table td.action-cell {
    padding: 8px 12px;
    text-align: center;
}

/* 针对不同按钮组合的情况 */
.action-cell .action-btn {
    margin: 2px;
}

/* 如果有三个按钮的情况，进一步调整 */
.action-cell:has(.action-btn:nth-child(3)) {
    min-width: 220px;
}

/* 个人首页表格样式 */
.personal-tables-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 30px;
}

.personal-table-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.personal-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.personal-table th {
    background: #f8f9fa;
    padding: 12px 8px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #e9ecef;
}

.personal-table td {
    padding: 10px 8px;
    border-bottom: 1px solid #e9ecef;
    color: #555;
}

.personal-table tbody tr:hover {
    background: #f8f9fa;
}

.personal-table .action-cell {
    text-align: center;
    white-space: nowrap;
}

.personal-table .action-btn {
    padding: 4px 8px;
    margin: 0 2px;
    font-size: 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.3s;
}

.personal-table .edit-btn {
    background: #007bff;
    color: white;
}

.personal-table .edit-btn:hover {
    background: #0056b3;
}

.personal-table .delete-btn {
    background: #dc3545;
    color: white;
}

.personal-table .delete-btn:hover {
    background: #c82333;
}
